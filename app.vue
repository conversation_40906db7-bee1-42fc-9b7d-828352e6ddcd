<template>
  <div>
    <NuxtPage />
    <!-- Debug cursor test -->
    <div
      v-if="showDebug"
      style="position: fixed; top: 10px; left: 10px; background: rgba(0,0,0,0.8); color: white; padding: 10px; z-index: 10001; font-size: 12px;"
    >
      <div>Cursor Plugin Debug:</div>
      <div>Mouse: {{ mousePos.x }}, {{ mousePos.y }}</div>
      <div>Cursor Elements: {{ cursorElements }}</div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount } from 'vue';

// This ensures the cursor plugin is loaded
console.log('🚀 App.vue mounted');

const showDebug = ref(true);
const mousePos = ref({ x: 0, y: 0 });
const cursorElements = ref({
  cursor: false,
  dot: false,
  trails: 0
});

onMounted(() => {
  // Track mouse position for debugging
  const updateMousePos = (e) => {
    mousePos.value = { x: e.clientX, y: e.clientY };
  };

  document.addEventListener('mousemove', updateMousePos);

  // Check for cursor elements periodically
  const checkCursorElements = () => {
    cursorElements.value = {
      cursor: !!document.querySelector('.custom-cursor'),
      dot: !!document.querySelector('.custom-cursor-dot'),
      trails: document.querySelectorAll('.custom-cursor-trail').length
    };
  };

  const interval = setInterval(checkCursorElements, 1000);

  // Cleanup
  onBeforeUnmount(() => {
    document.removeEventListener('mousemove', updateMousePos);
    clearInterval(interval);
  });
});
</script>

<style>
/* Global cursor override for debugging */
* {
  cursor: none !important;
}
</style>
