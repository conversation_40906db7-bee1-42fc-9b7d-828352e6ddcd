@import url('https://fonts.googleapis.com/css2?family=Manrope:wght@200..800&family=Open+Sans:ital,wght@0,300..800;1,300..800&family=Oswald:wght@200..700&display=swap');
@import "tailwindcss";

@theme {
  --color-primary-1: #4628A6;
  --color-primary-2: #5D35DD;
  --color-primary-3: #AF87FF;
  --color-neutral-1: #020315;
  --color-neutral-2: #242533;
  --color-neutral-3: #595880;
  --color-neutral-4: #7477A6;
  --color-neutral-5: #A3A5CC;
  --color-neutral-6: #CFD0E5;
  --color-neutral-7: #FFFFFF;
}

/* Custom CSS can be added here */

/* Typography Styles based on Manrope */

@layer base {
  body {
    font-family: 'Manrope', sans-serif;
    background-color: var(--color-neutral-1);
    color: var(--color-neutral-7);
  }

  /* Ensure Manrope font is available globally */
  .font-manrope {
    font-family: 'Manrope', sans-serif;
  }

  /* Background gradient utilities */
  .bg-gradient-radial-hero {
    background-image: radial-gradient(
      circle at
      calc(50% + var(--mouse-x-offset, 0px))
      calc(var(--gradient-y-pos, 0%) + var(--mouse-y-offset, 0px)),
      rgba(2, 3, 21, 0) 46.78%,
      rgba(93, 53, 221, 0.7) 72.55%,
      #5D35DD 87.11%,
      #AF87FF 100%
    );
    transition: background-position 0.1s ease-out;
    position: relative;
    overflow: hidden;
  }

  /* Star particle styles */
  .star-particle {
    pointer-events: none;
  }

  /* Animation helper classes */
  .gsap-animating {
    /* Class added by GSAP animations for tracking */
  }

  /* Custom Cursor Styles */
  .custom-cursor {
    position: fixed;
    width: 20px;
    height: 20px;
    background: rgba(255, 0, 0, 0.9) !important; /* Red for debugging */
    border: 2px solid rgba(255, 255, 255, 0.6);
    border-radius: 50%;
    pointer-events: none;
    z-index: 9999;
    transition: transform 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
    box-shadow: 0 0 15px rgba(255, 255, 255, 0.5);
    display: block !important; /* Force display */
    visibility: visible !important; /* Force visibility */
  }

  .custom-cursor-dot {
    position: fixed;
    width: 4px;
    height: 4px;
    background-color: blue !important; /* Blue for debugging */
    border-radius: 50%;
    pointer-events: none;
    z-index: 10000;
    box-shadow: 0 0 4px rgba(255, 255, 255, 0.6);
    display: block !important; /* Force display */
    visibility: visible !important; /* Force visibility */
  }

  .custom-cursor-trail {
    position: fixed;
    width: 10px;
    height: 10px;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.6) 0%, transparent 70%);
    border-radius: 50%;
    pointer-events: none;
    z-index: 9998;
  }

  .cursor-particle {
    position: fixed;
    width: 6px;
    height: 6px;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 50%;
    pointer-events: none;
    z-index: 9997;
    box-shadow: 0 0 8px rgba(255, 255, 255, 0.4);
  }

  /* Hide cursor on touch devices */
  @media (hover: none) and (pointer: coarse) {
    .custom-cursor,
    .custom-cursor-dot,
    .custom-cursor-trail,
    .cursor-particle {
      display: none !important;
    }
  }

  /* Smooth scrolling behavior */
  html {
    scroll-behavior: smooth;
  }

  /* Reduce motion for users who prefer it */
  @media (prefers-reduced-motion: reduce) {
    .scroll-section,
    .parallax-element,
    .scroll-reveal {
      animation: none !important;
      transition: none !important;
    }

    html {
      scroll-behavior: auto;
    }
  }
}

/* Headlines */
.h1 {
  font-family: 'Manrope', sans-serif;
  font-size: 6rem; /* 96px */
  font-weight: 800; /* Extrabold */
}

.h2 {
  font-family: 'Manrope', sans-serif;
  font-size: 3.5rem; /* 56px */
  font-weight: 800; /* Extrabold */
}

.h3 {
  font-family: 'Manrope', sans-serif;
  font-size: 2.5rem; /* 40px */
  font-weight: 700; /* Bold */
}

.h4-extrabold {
  font-family: 'Manrope', sans-serif;
  font-size: 2rem; /* 32px */
  font-weight: 800; /* Extrabold */
}

.h4-bold {
  font-family: 'Manrope', sans-serif;
  font-size: 2rem; /* 32px */
  font-weight: 700; /* Bold */
}

.h5-bold {
  font-family: 'Manrope', sans-serif;
  font-size: 1.5rem; /* 24px */
  font-weight: 700; /* Bold */
}

.h5-semibold {
  font-family: 'Manrope', sans-serif;
  font-size: 1.5rem; /* 24px */
  font-weight: 600; /* Semibold */
}

.h5-regular {
  font-family: 'Manrope', sans-serif;
  font-size: 1.5rem; /* 24px */
  font-weight: 400; /* Regular */
}

/* Body Text */
.body-1-bold {
  font-family: 'Manrope', sans-serif;
  font-size: 1.25rem; /* 20px */
  font-weight: 700; /* Bold */
}

.body-1-semibold {
  font-family: 'Manrope', sans-serif;
  font-size: 1.25rem; /* 20px */
  font-weight: 600; /* Semibold */
}

.body-1-regular {
  font-family: 'Manrope', sans-serif;
  font-size: 1.25rem; /* 20px */
  font-weight: 400; /* Regular */
}

.body-2-bold {
  font-family: 'Manrope', sans-serif;
  font-size: 1rem; /* 16px */
  font-weight: 700; /* Bold */
}

.body-2-regular {
  font-family: 'Manrope', sans-serif;
  font-size: 1rem; /* 16px */
  font-weight: 400; /* Regular */
}

.body-3-bold {
  font-family: 'Manrope', sans-serif;
  font-size: 0.875rem; /* 14px */
  font-weight: 700; /* Bold */
}

.body-3-semibold {
  font-family: 'Manrope', sans-serif;
  font-size: 0.875rem; /* 14px */
  font-weight: 600; /* Semibold */
}

.body-3-regular {
  font-family: 'Manrope', sans-serif;
  font-size: 0.875rem; /* 14px */
  font-weight: 400; /* Regular */
}

/* Labels */
.label-1 {
  font-family: 'Manrope', sans-serif;
  font-size: 1rem; /* 16px */
  font-weight: 700; /* Bold */
}

.label-2 {
  font-family: 'Manrope', sans-serif;
  font-size: 0.875rem; /* 14px */
  font-weight: 700; /* Bold */
}

/* Mobile responsive typography */
@media (max-width: 767px) {
  .h1 { /* Fullstack Developer */
    font-size: 3.5rem; /* 56px */
  }
  .h5-semibold { /* Hello, I'm Huy Nguyen */
    font-size: 1.25rem; /* 20px */
  }
  .text-body-2 { /* Button text */
    font-size: 0.875rem; /* 14px */
  }
  .h2 { /* Stats numbers: 6+, 24, 2+ */
    font-size: 2rem; /* 32px */
  }
  .body-1-semibold { /* Stats text */
    font-size: 0.875rem; /* 14px */
  }
}
