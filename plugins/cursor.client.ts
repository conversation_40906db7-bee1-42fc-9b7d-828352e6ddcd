import { useCursor } from '~/composables/useCursor';

/**
 * Global cursor plugin for Nuxt.js
 * Initializes custom cursor animations across the entire application
 */
export default defineNuxtPlugin(() => {
  // Only run on client-side
  if (!import.meta.client) return;

  console.log('🔍 Cursor plugin loading...');

  // Check if device supports hover (desktop only)
  const supportsHover = window.matchMedia('(hover: hover)').matches;
  console.log('🖱️ Device supports hover:', supportsHover);
  if (!supportsHover) {
    console.log('❌ Cursor disabled: Device does not support hover');
    return;
  }
  
  // Initialize cursor with global settings
  console.log('🎯 Initializing cursor composable...');
  const { initCursor, addHoverEffect } = useCursor({
    size: 24,
    trailLength: 10,
    followSpeed: 0.12,
    hoverScale: 1.8,
    particleCount: 4,
    enabled: true
  });
  console.log('✅ Cursor composable initialized');
  
  // Initialize cursor after DOM is ready
  const initGlobalCursor = () => {
    console.log('🚀 Starting cursor initialization...');
    try {
      // Small delay to ensure all components are mounted
      setTimeout(() => {
        console.log('⏰ Timeout reached, calling initCursor...');
        initCursor();
        
        // Add hover effects to common interactive elements
        addHoverEffect('a');
        addHoverEffect('button');
        addHoverEffect('[role="button"]');
        addHoverEffect('.btn');
        addHoverEffect('.cursor-hover');
        
        // Portfolio-specific elements
        addHoverEffect('.project-card');
        addHoverEffect('.skill-card');
        addHoverEffect('.expertise-item');
        addHoverEffect('.experience-card');
        addHoverEffect('.education-card');
        addHoverEffect('.social-link');
        addHoverEffect('.nav-link');
        addHoverEffect('.menu-item');
        addHoverEffect('.contact-button');
        
        console.log('✨ Custom cursor initialized successfully');
        console.log('Cursor elements created:', {
          cursor: document.querySelector('.custom-cursor'),
          dot: document.querySelector('.custom-cursor-dot'),
          trails: document.querySelectorAll('.custom-cursor-trail').length
        });
      }, 300);
    } catch (error) {
      console.warn('Failed to initialize global cursor:', error);
    }
  };
  
  // Initialize on DOM ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initGlobalCursor);
  } else {
    initGlobalCursor();
  }
  
  // Re-initialize cursor on route changes (for SPA navigation)
  const router = useRouter();
  router.afterEach(() => {
    setTimeout(() => {
      // Re-add hover effects to new elements after route change
      addHoverEffect('.project-card');
      addHoverEffect('.skill-card');
      addHoverEffect('.expertise-item');
      addHoverEffect('.experience-card');
      addHoverEffect('.education-card');
      addHoverEffect('.social-link');
      addHoverEffect('.nav-link');
      addHoverEffect('.menu-item');
      addHoverEffect('.contact-button');
    }, 100);
  });
});