import { ref, readonly, onMounted, onBeforeUnmount } from 'vue';
import { useAnimations } from './useAnimations';

export interface CursorOptions {
  size?: number;
  trailLength?: number;
  followSpeed?: number;
  hoverScale?: number;
  particleCount?: number;
  enabled?: boolean;
}

/**
 * Composable for managing custom cursor animations
 * Provides easy integration with Vue components
 */
export const useCursor = (options: CursorOptions = {}) => {
  const { initCustomCursor, addCursorHover } = useAnimations();
  
  const isEnabled = ref(options.enabled ?? true);
  const isInitialized = ref(false);
  
  let cleanupCursor: (() => void) | null = null;
  
  /**
   * Initialize the custom cursor
   */
  const initCursor = () => {
    console.log('🎯 useCursor.initCursor called');
    console.log('📊 Cursor state:', {
      isClient: import.meta.client,
      isEnabled: isEnabled.value,
      isInitialized: isInitialized.value
    });

    if (!import.meta.client || !isEnabled.value || isInitialized.value) {
      console.log('❌ Cursor init skipped due to conditions');
      return;
    }

    // Check if device supports hover (desktop)
    const supportsHover = window.matchMedia('(hover: hover)').matches;
    console.log('🖱️ Device supports hover:', supportsHover);
    if (!supportsHover) {
      console.log('❌ Cursor init skipped: no hover support');
      return;
    }
    
    try {
      console.log('🎨 Calling initCustomCursor with options:', {
        size: options.size ?? 20,
        trailLength: options.trailLength ?? 8,
        followSpeed: options.followSpeed ?? 0.15,
        hoverScale: options.hoverScale ?? 1.5,
        particleCount: options.particleCount ?? 3
      });

      cleanupCursor = initCustomCursor({
        size: options.size ?? 20,
        trailLength: options.trailLength ?? 8,
        followSpeed: options.followSpeed ?? 0.15,
        hoverScale: options.hoverScale ?? 1.5,
        particleCount: options.particleCount ?? 3
      });

      console.log('✅ Custom cursor initialized successfully');
      isInitialized.value = true;
    } catch (error) {
      console.error('❌ Failed to initialize custom cursor:', error);
    }
  };
  
  /**
   * Destroy the custom cursor
   */
  const destroyCursor = () => {
    if (cleanupCursor) {
      cleanupCursor();
      cleanupCursor = null;
      isInitialized.value = false;
    }
  };
  
  /**
   * Enable cursor animations
   */
  const enableCursor = () => {
    isEnabled.value = true;
    if (import.meta.client) {
      initCursor();
    }
  };
  
  /**
   * Disable cursor animations
   */
  const disableCursor = () => {
    isEnabled.value = false;
    destroyCursor();
  };
  
  /**
   * Add hover effects to specific elements
   */
  const addHoverEffect = (selector: string) => {
    if (import.meta.client && isInitialized.value) {
      addCursorHover(selector);
    }
  };
  
  /**
   * Toggle cursor on/off
   */
  const toggleCursor = () => {
    if (isEnabled.value) {
      disableCursor();
    } else {
      enableCursor();
    }
  };
  
  // Auto-initialize on mount
  onMounted(() => {
    if (isEnabled.value) {
      // Small delay to ensure DOM is ready
      setTimeout(initCursor, 100);
    }
  });
  
  // Cleanup on unmount
  onBeforeUnmount(() => {
    destroyCursor();
  });
  
  return {
    isEnabled: readonly(isEnabled),
    isInitialized: readonly(isInitialized),
    initCursor,
    destroyCursor,
    enableCursor,
    disableCursor,
    addHoverEffect,
    toggleCursor
  };
};

/**
 * Global cursor state for app-wide cursor management
 */
const globalCursorState = ref<{
  enabled: boolean;
  initialized: boolean;
}>({ enabled: true, initialized: false });

/**
 * Global cursor composable for app-wide cursor control
 */
export const useGlobalCursor = () => {
  return {
    state: readonly(globalCursorState),
    enable: () => { globalCursorState.value.enabled = true; },
    disable: () => { globalCursorState.value.enabled = false; },
    setInitialized: (value: boolean) => { globalCursorState.value.initialized = value; }
  };
};