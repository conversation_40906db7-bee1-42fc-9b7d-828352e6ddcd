import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';

gsap.registerPlugin(ScrollTrigger);

export interface AnimationOptions {
  duration?: number;
  delay?: number;
  stagger?: number;
  ease?: string;
}

export interface ScrollTriggerOptions {
  trigger?: string | Element;
  start?: string;
  end?: string;
  scrub?: boolean | number;
  pin?: boolean;
  markers?: boolean;
}

/**
 * Composable for handling GSAP animations throughout the application
 */
export const useAnimations = () => {

  /**
   * Animate elements in with elastic effect (for skills)
   */
  const animateSkillsIn = (
    items: NodeListOf<Element> | Element[],
    options: AnimationOptions = {}
  ): Promise<void> => {
    const {
      duration = 1,
      stagger = 0.1,
      ease = 'elastic.out(1, 0.75)'
    } = options;

    return new Promise((resolve) => {
      if (!items || items.length === 0) {
        resolve();
        return;
      }

      gsap.fromTo(items,
        { opacity: 0, scale: 0.2, y: 75, x: -30, rotation: -15 },
        {
          opacity: 1,
          scale: 1,
          y: 0,
          x: 0,
          rotation: 0,
          duration,
          stagger,
          ease,
          overwrite: 'auto',
          onComplete: resolve,
        }
      );
    });
  };

  /**
   * Animate elements out with smooth transition
   */
  const animateSkillsOut = (
    items: NodeListOf<Element> | Element[],
    options: AnimationOptions = {}
  ): Promise<void> => {
    const {
      duration = 0.4,
      stagger = 0.05,
      ease = 'power2.in'
    } = options;

    return new Promise((resolve) => {
      if (!items || items.length === 0) {
        resolve();
        return;
      }

      gsap.to(items, {
        opacity: 0,
        scale: 0.5,
        y: -30,
        rotation: 10,
        duration,
        stagger,
        ease,
        overwrite: 'auto',
        onComplete: resolve,
      });
    });
  };

  /**
   * Animate header elements on mount
   */
  const animateHeaderIn = (
    navElement: Element | null,
    contactButton: Element | null,
    mobileElements: NodeListOf<Element> | null,
    options: AnimationOptions = {}
  ) => {
    const {
      duration = 0.5,
      stagger = 0.1,
      delay = 0.3,
      ease = 'power3.out'
    } = options;

    if (!navElement && !contactButton && !mobileElements) return;

    const tl = gsap.timeline({ defaults: { ease } });

    // Animate desktop navigation items
    if (navElement && navElement.children) {
      tl.from(navElement.children, {
        opacity: 0,
        y: -20,
        duration,
        stagger,
        delay
      });
    }

    // Animate desktop contact button
    if (contactButton) {
      tl.from(contactButton, {
        opacity: 0,
        y: -20,
        duration
      }, "-=0.3");
    }

    // Animate mobile elements
    if (mobileElements && mobileElements.length > 0) {
      tl.from(mobileElements, {
        opacity: 0,
        y: -20,
        duration,
        stagger,
        delay
      }, 0);
    }
  };

  /**
   * Animate masthead elements
   */
  const animateMastheadIn = (
    greeting: Element | null,
    title: Element | null,
    buttons: Element | null,
    description: Element | null,
    image: Element | null,
    options: AnimationOptions = {}
  ) => {
    const {
      duration = 0.8,
      ease = 'power3.out'
    } = options;

    const tl = gsap.timeline({ defaults: { ease } });

    if (greeting) {
      tl.from(greeting, {
        opacity: 0,
        y: 30,
        duration
      });
    }

    if (title) {
      tl.from(title, {
        opacity: 0,
        y: 50,
        duration
      }, `-=${duration * 0.7}`);
    }

    if (description) {
      tl.from(description, {
        opacity: 0,
        y: 30,
        duration
      }, `-=${duration * 0.5}`);
    }

    if (buttons) {
      tl.from(buttons.children, {
        opacity: 0,
        y: 30,
        duration,
        stagger: 0.1
      }, `-=${duration * 0.3}`);
    }

    if (image) {
      tl.from(image, {
        opacity: 0,
        scale: 0.8,
        duration: duration * 1.2
      }, `-=${duration * 0.8}`);
    }
  };

  /**
   * Create floating star particles
   */
  const createStarParticles = (
    container: HTMLElement,
    count: number = 50
  ) => {
    if (!container) return;

    for (let i = 0; i < count; i++) {
      const star = document.createElement('div');
      star.classList.add('star-particle');

      const size = gsap.utils.random(1, 4);
      star.style.width = `${size}px`;
      star.style.height = `${size}px`;
      star.style.backgroundColor = 'white';
      star.style.borderRadius = '50%';
      star.style.position = 'absolute';
      star.style.opacity = `${gsap.utils.random(0.3, 0.8)}`;

      const x = gsap.utils.random(0, container.offsetWidth);
      const y = gsap.utils.random(0, container.offsetHeight);
      gsap.set(star, { x, y });

      container.appendChild(star);

      // Animate the star
      gsap.to(star, {
        y: y - gsap.utils.random(50, 200),
        opacity: 0,
        duration: gsap.utils.random(3, 6),
        repeat: -1,
        repeatDelay: gsap.utils.random(0, 2),
        ease: 'none'
      });
    }
  };

  /**
   * Animate expertise items with ScrollTrigger
   */
  const animateExpertiseItems = (
    container: Element | null,
    options: ScrollTriggerOptions = {}
  ): gsap.core.Timeline | undefined => {
    if (!container) return undefined;

    const items = container.querySelectorAll('.expertise-item');
    if (!items.length) return undefined;

    // Kill previous ScrollTriggers for expertise items
    ScrollTrigger.getAll().forEach(st => {
      const stTrigger = st.trigger;
      if (stTrigger && typeof (stTrigger as HTMLElement).matches === 'function' &&
          (stTrigger as HTMLElement).matches('.expertise-item')) {
        st.kill();
      }
    });

    // Create a single ScrollTrigger for the container
    const tl = gsap.timeline({
      scrollTrigger: {
        trigger: container,
        start: 'top 75%',
        end: 'bottom 15%',
        toggleActions: 'play none none reverse',
        // markers: true, // Uncomment for debugging
        ...options
      }
    });

    // Add staggered animations to the timeline
    tl.fromTo(items,
      { opacity: 0, y: 50, scale: 0.95 },
      {
        opacity: 1,
        y: 0,
        scale: 1,
        duration: 0.8,
        stagger: {
          amount: 0.6, // Total stagger time
          from: "start",
          grid: "auto",
          ease: "power2.inOut"
        },
        ease: 'back.out(1.2)',
        clearProps: "all" // Improves performance after animation
      }
    );

    return tl;
  };

  /**
   * Generic fade in animation with ScrollTrigger (enhanced)
   */
  const fadeInOnScroll = (
    elements: string | Element | NodeListOf<Element>,
    options: ScrollTriggerOptions & AnimationOptions = {}
  ) => {
    const {
      duration = 0.8,
      stagger = 0.1,
      ease = 'power2.out',
      start = 'top 80%',
      end = 'bottom 20%',
      ...scrollOptions
    } = options;

    let targets: Element[] = [];

    if (typeof elements === 'string') {
      targets = Array.from(document.querySelectorAll(elements));
    } else if (elements instanceof Element) {
      targets = [elements];
    } else if (elements instanceof NodeList) {
      targets = Array.from(elements);
    }

    targets.forEach((element, index) => {
      gsap.fromTo(element,
        { opacity: 0, y: 30, scale: 0.95 },
        {
          opacity: 1,
          y: 0,
          scale: 1,
          duration,
          ease,
          delay: stagger * index,
          scrollTrigger: {
            trigger: element,
            start,
            end,
            toggleActions: 'play none none reverse',
            invalidateOnRefresh: true,
            ...scrollOptions
          }
        }
      );
    });
  };

  /**
   * Enhanced parallax effect for backgrounds and elements
   */
  const createParallaxBackground = (
    element: string | Element,
    options: {
      speed?: number;
      direction?: 'up' | 'down';
      distance?: number;
    } = {}
  ) => {
    const { speed = 0.5, direction = 'up', distance = 100 } = options;

    const target = typeof element === 'string'
      ? document.querySelector(element)
      : element;

    if (!target) return;

    const yMovement = direction === 'up' ? -distance * speed : distance * speed;

    gsap.to(target, {
      y: yMovement,
      ease: 'none',
      scrollTrigger: {
        trigger: target,
        start: 'top bottom',
        end: 'bottom top',
        scrub: true,
        invalidateOnRefresh: true
      }
    });
  };

  /**
   * Custom cursor animation system
   */
  const initCustomCursor = (options: {
    size?: number;
    trailLength?: number;
    followSpeed?: number;
    hoverScale?: number;
    particleCount?: number;
  } = {}) => {
    console.log('🎨 useAnimations.initCustomCursor called with options:', options);

    const {
      size = 20,
      trailLength = 8,
      followSpeed = 0.15,
      hoverScale = 1.5,
      particleCount = 3
    } = options;

    console.log('📐 Cursor configuration:', { size, trailLength, followSpeed, hoverScale, particleCount });

    // Create cursor elements
    console.log('🔨 Creating cursor elements...');
    const cursor = document.createElement('div');
    const cursorDot = document.createElement('div');
    const cursorTrail: HTMLElement[] = [];

    // Setup cursor styles
    cursor.className = 'custom-cursor';
    cursorDot.className = 'custom-cursor-dot';
    console.log('✅ Cursor elements created:', { cursor, cursorDot });
    
    // Set initial position and visibility using direct CSS
    cursor.style.cssText = `
      position: fixed !important;
      left: 200px !important;
      top: 200px !important;
      width: ${size}px !important;
      height: ${size}px !important;
      opacity: 1 !important;
      transform: none !important;
      z-index: 9999 !important;
      background: red !important;
      border-radius: 50% !important;
      pointer-events: none !important;
      display: block !important;
      visibility: visible !important;
    `;

    cursorDot.style.cssText = `
      position: fixed !important;
      left: 220px !important;
      top: 220px !important;
      width: 4px !important;
      height: 4px !important;
      opacity: 1 !important;
      transform: none !important;
      z-index: 10000 !important;
      background: blue !important;
      border-radius: 50% !important;
      pointer-events: none !important;
      display: block !important;
      visibility: visible !important;
    `;
    
    // Add cursor to DOM
    console.log('📍 Adding cursor elements to DOM...');
    document.body.appendChild(cursor);
    document.body.appendChild(cursorDot);
    console.log('✅ Cursor elements added to DOM');
    
    // Create trail elements
    for (let i = 0; i < trailLength; i++) {
      const trail = document.createElement('div');
      trail.className = 'custom-cursor-trail';
      
      // Set initial position
      gsap.set(trail, {
        x: 120 + (i * 5), // Stagger trails for debugging
        y: 120 + (i * 5),
        width: size / 2,
        height: size / 2,
        opacity: 0.5,
        scale: 1
      });
      
      document.body.appendChild(trail);
      cursorTrail.push(trail);
    }
    
    // Mouse position tracking
    let mouseX = window.innerWidth / 2;
    let mouseY = window.innerHeight / 2;
    let cursorX = mouseX;
    let cursorY = mouseY;

    console.log('🎯 Initial mouse position:', { mouseX, mouseY, windowSize: { width: window.innerWidth, height: window.innerHeight } });
    
    // Trail positions
    const trailPositions: { x: number; y: number }[] = [];
    for (let i = 0; i < trailLength; i++) {
      trailPositions.push({ x: mouseX, y: mouseY });
    }
    
    // Update mouse position
    const updateMousePosition = (e: MouseEvent) => {
      mouseX = e.clientX;
      mouseY = e.clientY;
    };
    
    // Animate cursor following
    const animateCursor = () => {
      // Smooth cursor following
      cursorX += (mouseX - cursorX) * followSpeed;
      cursorY += (mouseY - cursorY) * followSpeed;

      // Debug log positions
      if (Math.random() < 0.01) { // Log occasionally to avoid spam
        console.log('🎯 Cursor positions:', {
          mouse: { x: mouseX, y: mouseY },
          cursor: { x: cursorX, y: cursorY },
          finalCursor: { x: cursorX - size / 2, y: cursorY - size / 2 },
          finalDot: { x: mouseX - 2, y: mouseY - 2 }
        });
      }

      // Update main cursor position
      gsap.set(cursor, {
        x: cursorX - size / 2,
        y: cursorY - size / 2,
        force3D: true
      });

      gsap.set(cursorDot, {
        x: mouseX - 2,
        y: mouseY - 2,
        force3D: true
      });
      
      // Update trail positions
      for (let i = trailLength - 1; i > 0; i--) {
        trailPositions[i].x = trailPositions[i - 1].x;
        trailPositions[i].y = trailPositions[i - 1].y;
      }
      trailPositions[0].x = cursorX;
      trailPositions[0].y = cursorY;
      
      // Animate trail elements
      cursorTrail.forEach((trail, index) => {
        const opacity = (trailLength - index) / trailLength * 0.3;
        const scale = (trailLength - index) / trailLength * 0.8;
        
        gsap.set(trail, {
          x: trailPositions[index].x - size / 4,
          y: trailPositions[index].y - size / 4,
          opacity,
          scale
        });
      });
      
      requestAnimationFrame(animateCursor);
    };
    
    // Handle hover effects
    const handleHoverIn = (element: Element) => {
      gsap.to(cursor, {
        scale: hoverScale,
        duration: 0.3,
        ease: 'back.out(1.7)'
      });
      
      // Add glow effect
      cursor.style.boxShadow = '0 0 20px rgba(93, 53, 221, 0.6)';
      
      // Create hover particles
      createHoverParticles(mouseX, mouseY, particleCount);
    };
    
    const handleHoverOut = () => {
      gsap.to(cursor, {
        scale: 1,
        duration: 0.3,
        ease: 'back.out(1.7)'
      });
      
      cursor.style.boxShadow = 'none';
    };
    
    // Create particles on hover
    const createHoverParticles = (x: number, y: number, count: number) => {
      for (let i = 0; i < count; i++) {
        const particle = document.createElement('div');
        particle.className = 'cursor-particle';
        document.body.appendChild(particle);
        
        const angle = (360 / count) * i;
        const distance = gsap.utils.random(20, 40);
        const endX = x + Math.cos(angle * Math.PI / 180) * distance;
        const endY = y + Math.sin(angle * Math.PI / 180) * distance;
        
        gsap.set(particle, { x, y });
        
        gsap.to(particle, {
          x: endX,
          y: endY,
          opacity: 0,
          scale: 0,
          duration: 0.6,
          ease: 'power2.out',
          onComplete: () => {
            document.body.removeChild(particle);
          }
        });
      }
    };
    
    // Setup event listeners
    document.addEventListener('mousemove', updateMousePosition);
    
    // Add hover listeners to interactive elements
    const interactiveElements = document.querySelectorAll(
      'a, button, [role="button"], .cursor-hover'
    );
    
    interactiveElements.forEach(element => {
      element.addEventListener('mouseenter', () => handleHoverIn(element));
      element.addEventListener('mouseleave', handleHoverOut);
    });
    
    // Hide default cursor
    console.log('🚫 Hiding default cursor...');
    document.body.style.cursor = 'none';
    document.documentElement.style.cursor = 'none';

    // Start animation
    console.log('🎬 Starting cursor animation...');
    // Temporarily disable animation loop for debugging
    // animateCursor();

    // Instead, just update on mouse move using direct CSS
    document.addEventListener('mousemove', (e) => {
      cursor.style.left = (e.clientX - size / 2) + 'px';
      cursor.style.top = (e.clientY - size / 2) + 'px';

      cursorDot.style.left = (e.clientX - 2) + 'px';
      cursorDot.style.top = (e.clientY - 2) + 'px';

      // Debug log occasionally
      if (Math.random() < 0.01) {
        console.log('🖱️ Mouse move:', {
          mouse: { x: e.clientX, y: e.clientY },
          cursorPos: { left: cursor.style.left, top: cursor.style.top },
          dotPos: { left: cursorDot.style.left, top: cursorDot.style.top }
        });
      }
    });

    // Debug: Check if elements are visible after a short delay
    setTimeout(() => {
      const cursorStyle = window.getComputedStyle(cursor);
      const cursorDotStyle = window.getComputedStyle(cursorDot);

      console.log('🔍 Cursor debug check:', {
        cursorElement: cursor,
        cursorDotElement: cursorDot,
        cursorInDOM: document.body.contains(cursor),
        cursorDotInDOM: document.body.contains(cursorDot),
        cursorPosition: {
          left: cursorStyle.left,
          top: cursorStyle.top,
          transform: cursorStyle.transform,
          display: cursorStyle.display,
          visibility: cursorStyle.visibility,
          opacity: cursorStyle.opacity,
          zIndex: cursorStyle.zIndex
        },
        cursorDotPosition: {
          left: cursorDotStyle.left,
          top: cursorDotStyle.top,
          transform: cursorDotStyle.transform,
          display: cursorDotStyle.display,
          visibility: cursorDotStyle.visibility,
          opacity: cursorDotStyle.opacity,
          zIndex: cursorDotStyle.zIndex
        },
        bodyStyle: document.body.style.cursor,
        mousePosition: { mouseX, mouseY }
      });

      // Force visibility for debugging
      cursor.style.cssText += 'display: block !important; visibility: visible !important; opacity: 1 !important; background: red !important;';
      cursorDot.style.cssText += 'display: block !important; visibility: visible !important; opacity: 1 !important; background: blue !important;';

    }, 1000);

    // Return cleanup function
    return () => {
      document.removeEventListener('mousemove', updateMousePosition);
      document.body.style.cursor = 'auto';
      
      // Remove cursor elements
      if (cursor.parentNode) cursor.parentNode.removeChild(cursor);
      if (cursorDot.parentNode) cursorDot.parentNode.removeChild(cursorDot);
      cursorTrail.forEach(trail => {
        if (trail.parentNode) trail.parentNode.removeChild(trail);
      });
      
      // Remove hover listeners
      interactiveElements.forEach(element => {
        element.removeEventListener('mouseenter', () => handleHoverIn(element));
        element.removeEventListener('mouseleave', handleHoverOut);
      });
    };
  };
  
  /**
   * Add cursor hover class to elements
   */
  const addCursorHover = (selector: string) => {
    const elements = document.querySelectorAll(selector);
    elements.forEach(element => {
      element.classList.add('cursor-hover');
    });
  };
  
  /**
   * Cleanup all ScrollTriggers
   */
  const cleanupScrollTriggers = () => {
    ScrollTrigger.getAll().forEach(st => st.kill());
  };

  return {
    animateSkillsIn,
    animateSkillsOut,
    animateHeaderIn,
    animateMastheadIn,
    createStarParticles,
    animateExpertiseItems,
    fadeInOnScroll,
    createParallaxBackground,
    initCustomCursor,
    addCursorHover,
    cleanupScrollTriggers
  };
};
