# Cursor Animation System

A comprehensive custom cursor animation system built with GSAP for the Portfolio 2025 project. The system provides smooth cursor following, interactive hover effects, particle trails, and seamless integration with the existing design system.

## Features

- **Smooth Cursor Following**: Fluid cursor movement with customizable follow speed
- **Interactive Hover Effects**: Scale animations and glow effects on interactive elements
- **Particle Trails**: Dynamic trailing particles that follow cursor movement
- **Hover Particles**: Burst particles on element hover
- **Touch Device Detection**: Automatically disabled on touch devices
- **Performance Optimized**: Uses GSAP for smooth 60fps animations
- **Design System Integration**: Uses project color variables and follows design patterns

## Architecture

### Core Components

1. **useAnimations.ts** - Core GSAP animation functions
2. **useCursor.ts** - Vue composable for cursor management
3. **cursor.client.ts** - Global Nuxt plugin for app-wide initialization
4. **main.css** - CSS styles for cursor elements

### File Structure

```
composables/
├── useAnimations.ts     # Core cursor animation functions
└── useCursor.ts         # Vue composable wrapper

plugins/
└── cursor.client.ts     # Global initialization plugin

assets/css/
└── main.css            # Cursor element styles

docs/
└── cursor-animations.md # This documentation
```

## Usage

### Automatic Initialization

The cursor system is automatically initialized globally via the Nuxt plugin. No manual setup required.

### Manual Control

For component-level control, use the `useCursor` composable:

```vue
<script setup lang="ts">
import { useCursor } from '~/composables/useCursor';

const {
  isEnabled,
  isInitialized,
  enableCursor,
  disableCursor,
  addHoverEffect,
  toggleCursor
} = useCursor({
  size: 24,
  trailLength: 10,
  followSpeed: 0.12,
  hoverScale: 1.8,
  particleCount: 4
});

// Add hover effects to specific elements
onMounted(() => {
  addHoverEffect('.my-custom-element');
});
</script>
```

### Adding Hover Effects

To add cursor hover effects to elements:

```typescript
// Method 1: Using the composable
const { addHoverEffect } = useCursor();
addHoverEffect('.my-element');

// Method 2: Adding CSS class
// Elements with these classes automatically get hover effects:
// - .cursor-hover
// - a, button, [role="button"]
// - .project-card, .skill-card, etc.
```

```html
<!-- Method 3: Direct CSS class -->
<div class="cursor-hover">Hover me!</div>
```

## Configuration Options

### CursorOptions Interface

```typescript
interface CursorOptions {
  size?: number;          // Cursor size in pixels (default: 20)
  trailLength?: number;   // Number of trail elements (default: 8)
  followSpeed?: number;   // Follow speed 0-1 (default: 0.15)
  hoverScale?: number;    // Scale on hover (default: 1.5)
  particleCount?: number; // Particles on hover (default: 3)
  enabled?: boolean;      // Enable/disable cursor (default: true)
}
```

### Global Configuration

Modify the global settings in `plugins/cursor.client.ts`:

```typescript
const { initCursor } = useCursor({
  size: 24,           // Larger cursor
  trailLength: 10,    // More trail elements
  followSpeed: 0.12,  // Slower following
  hoverScale: 1.8,    // Bigger hover effect
  particleCount: 4    // More particles
});
```

## CSS Classes

### Cursor Elements

- `.custom-cursor` - Main cursor element
- `.custom-cursor-dot` - Center dot
- `.custom-cursor-trail` - Trail elements
- `.cursor-particle` - Hover particles

### Interactive Elements

Elements that automatically receive hover effects:

- `a` - Links
- `button` - Buttons
- `[role="button"]` - Button-like elements
- `.cursor-hover` - Custom hover class
- `.project-card` - Project cards
- `.skill-card` - Skill cards
- `.expertise-item` - Expertise items
- `.experience-card` - Experience cards
- `.education-card` - Education cards
- `.social-link` - Social links
- `.nav-link` - Navigation links
- `.menu-item` - Menu items
- `.contact-button` - Contact buttons

## Styling Customization

### Color Customization

Cursor colors use CSS custom properties from the design system:

```css
.custom-cursor {
  background: linear-gradient(135deg, var(--color-primary-2), var(--color-primary-3));
}

.custom-cursor-dot {
  background-color: var(--color-neutral-7);
}

.custom-cursor-trail {
  background: radial-gradient(circle, var(--color-primary-2) 0%, transparent 70%);
}
```

### Size Customization

```css
.custom-cursor {
  width: 24px;  /* Adjust size */
  height: 24px;
}

.custom-cursor-trail {
  width: 12px;  /* Adjust trail size */
  height: 12px;
}
```

## Performance Considerations

- **Touch Device Detection**: Cursor is automatically disabled on touch devices
- **RequestAnimationFrame**: Uses RAF for smooth 60fps animations
- **GSAP Optimization**: Leverages GSAP's optimized rendering
- **Element Cleanup**: Proper cleanup of DOM elements and event listeners
- **Memory Management**: Prevents memory leaks with proper unmounting

## Browser Support

- **Desktop**: Chrome, Firefox, Safari, Edge (latest versions)
- **Mobile**: Automatically disabled on touch devices
- **Fallback**: Graceful degradation to default cursor

## Troubleshooting

### Cursor Not Appearing

1. Check if device supports hover: `window.matchMedia('(hover: hover)').matches`
2. Verify GSAP is loaded
3. Check browser console for errors
4. Ensure elements have proper z-index

### Performance Issues

1. Reduce `trailLength` for better performance
2. Increase `followSpeed` for less smooth but faster cursor
3. Reduce `particleCount` for fewer hover particles

### Hover Effects Not Working

1. Ensure elements have the correct CSS classes
2. Check if `addHoverEffect()` is called after DOM elements exist
3. Verify event listeners are properly attached

## Development

### Adding New Cursor Effects

To add new cursor effects, extend the `useAnimations.ts` composable:

```typescript
const createCustomEffect = (x: number, y: number) => {
  // Custom GSAP animation
  gsap.to('.custom-cursor', {
    // Animation properties
  });
};
```

### Testing

```bash
# Start development server
npm run dev

# Test on different devices
# - Desktop: Full cursor functionality
# - Mobile: Cursor should be hidden
# - Tablet: Depends on hover capability
```

## Examples

### Custom Hover Effect

```vue
<template>
  <div class="my-component">
    <button class="special-button cursor-hover">
      Special Button
    </button>
  </div>
</template>

<script setup lang="ts">
import { useCursor } from '~/composables/useCursor';

const { addHoverEffect } = useCursor();

onMounted(() => {
  addHoverEffect('.special-button');
});
</script>
```

### Temporary Cursor Disable

```vue
<script setup lang="ts">
const { disableCursor, enableCursor } = useCursor();

// Disable during loading
const isLoading = ref(true);

watch(isLoading, (loading) => {
  if (loading) {
    disableCursor();
  } else {
    enableCursor();
  }
});
</script>
```

## Contributing

When contributing to the cursor system:

1. Follow the existing code patterns
2. Use TypeScript with proper typing
3. Test on multiple devices
4. Update documentation
5. Ensure performance optimization
6. Follow the project's design system colors

## License

Part of the Portfolio 2025 project. See main project license.